{"name": "netease_playlist_classifier", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/typography": "^0.5.15", "@vercel/analytics": "^1.4.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "daisyui": "^4.12.23", "echarts": "^5.6.0", "framer-motion": "^11.15.0", "i18next": "^24.2.0", "jotai": "^2.11.0", "mini-svg-data-uri": "^1.4.4", "path": "^0.12.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-i18next": "^15.3.0", "react-router-dom": "^7.1.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}