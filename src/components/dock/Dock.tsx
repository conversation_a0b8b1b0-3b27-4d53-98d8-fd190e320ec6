
import React from 'react';

import { useAtom } from 'jotai/index';
import {
  useLocation,
  useNavigate,
} from 'react-router-dom';

import {
  Dock,
  DockIcon,
} from '@/components/dock/DockFrame';
import { themeAtom } from '@/store/AppSet';

interface IconProps {
  className: string;
  isDark?: boolean; // 添加 isDark 属性
}


export function DockObject(): React.ReactElement {
  const navigate = useNavigate()
  const location = useLocation()
  // 暗色模式
  const [theme] = useAtom(themeAtom);
  const handleNavigation = (path: string) => {

    navigate(path)

  }




  return (
    <div className=" z-50  bg-background absolute  flex  bottom-4   flex-col items-center  justify-center rounded-lg h-20">

      <Dock className="relative  bg-[rgba(255,255,255)] hover:bg-[rgb(255,255,255)] dark:bg-zinc-800 dark:hover:bg-zinc-700 duration-200 hover:backdrop-blur-xl  ">
 

        <DockIcon
          name={'歌单'}
          designation={'想听什么就听什么🎵'}
          id={2}
          onClick={() => handleNavigation('/home')}
          className={
            (location.pathname === '/home' ? '  ' : '') + 'bg-neutral-400/20'
          }
        >
          {location.pathname !== '/home' ? (
            <Icons.Review className="h-6 w-6 dark:text-white" />
          ) : (
            <Icons.selectedReview className="h-6 w-6 dark:text-white" isDark={theme === 'dark'} />
          )}
        </DockIcon>
        {/* <DockIcon
          name={'介绍'}
          designation={'小鹿💖'}
          id={3}
          onClick={() => handleNavigation('/goods')}
          className={
            (location.pathname === '/goods' ? ' ' : '') + 'bg-neutral-400/20'
          }
        >
          {location.pathname !== '/goods' ? (
            <Icons.Content className="h-6 w-6 dark:text-white" />
          ) : (
            <Icons.selectedContent className="h-6 w-6 dark:text-white" isDark={theme === 'dark'} />

          )}
        </DockIcon> */}
        <DockIcon
          name={'设置'}
          designation={'在这进行相关设置💻'}
          id={4}
          onClick={() => handleNavigation('/setting')}
          className={
            (location.pathname === '/setting' ? '  ' : '') + 'bg-neutral-400/20'
          }
        >
          {location.pathname !== '/setting' ? (
            <Icons.Set className="h-6 w-6 dark:text-white" />
          ) : (
            <Icons.selectedSet className="h-6 w-6 dark:text-white" isDark={theme === 'dark'} />
          )}
        </DockIcon>


      </Dock>
    </div>
  )
}
const Icons = {
  Review: (_props: IconProps) => (

    <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1404_599)">
        <path d="M16 3C16.7652 2.99996 17.5015 3.29233 18.0583 3.81728C18.615 4.34224 18.9501 5.06011 18.995 5.824L19 6V16H19.75C20.397 16 20.93 16.492 20.994 17.122L21 17.25V19C21 19.7652 20.7077 20.5015 20.1827 21.0583C19.6578 21.615 18.9399 21.9501 18.176 21.995L18 22H8C7.23479 22 6.49849 21.7077 5.94174 21.1827C5.38499 20.6578 5.04989 19.9399 5.005 19.176L5 19V9H3.25C2.94054 9.00014 2.64203 8.88549 2.41223 8.67823C2.18244 8.47097 2.03769 8.18583 2.006 7.878L2 7.75V6C1.99996 5.23479 2.29233 4.49849 2.81728 3.94174C3.34224 3.38499 4.06011 3.04989 4.824 3.005L5 3H16ZM16 5H7V19C7 19.2652 7.10536 19.5196 7.29289 19.7071C7.48043 19.8946 7.73478 20 8 20C8.26522 20 8.51957 19.8946 8.70711 19.7071C8.89464 19.5196 9 19.2652 9 19V17.25C9 16.56 9.56 16 10.25 16H17V6C17 5.73478 16.8946 5.48043 16.7071 5.29289C16.5196 5.10536 16.2652 5 16 5ZM19 18H11V19C11 19.35 10.94 19.687 10.83 20H18C18.2652 20 18.5196 19.8946 18.7071 19.7071C18.8946 19.5196 19 19.2652 19 19V18ZM5 5C4.75507 5.00003 4.51866 5.08996 4.33563 5.25272C4.15259 5.41547 4.03566 5.63975 4.007 5.883L4 6V7H5V5Z" fill="#212833" />
        <path d="M7 5H16C16.2652 5 16.5196 5.10536 16.7071 5.29289C16.8946 5.48043 17 5.73478 17 6V16H10.25C9.56 16 9 16.56 9 17.25V19C9 19.2652 8.89464 19.5196 8.70711 19.7071C8.51957 19.8946 8.26522 20 8 20C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19V5Z" fill="#B8BABC" />
        <path d="M12.7071 12.2929C12.5196 12.1054 12.2652 12 12 12H10C9.73478 12 9.48043 12.1054 9.29289 12.2929C9.10536 12.4804 9 12.7348 9 13C9 13.2652 9.10536 13.5196 9.29289 13.7071C9.48043 13.8946 9.73478 14 10 14H12C12.2652 14 12.5196 13.8946 12.7071 13.7071C12.8946 13.5196 13 13.2652 13 13C13 12.7348 12.8946 12.4804 12.7071 12.2929Z" fill="#212833" />
        <path d="M14.7071 8.29289C14.5196 8.10536 14.2652 8 14 8H10C9.73478 8 9.48043 8.10536 9.29289 8.29289C9.10536 8.48043 9 8.73478 9 9C9 9.26522 9.10536 9.51957 9.29289 9.70711C9.48043 9.89464 9.73478 10 10 10H14C14.2652 10 14.5196 9.89464 14.7071 9.70711C14.8946 9.51957 15 9.26522 15 9C15 8.73478 14.8946 8.48043 14.7071 8.29289Z" fill="#212833" />
      </g>
      <defs>
        <clipPath id="clip0_1404_599">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),


  selectedReview: ({ isDark }: IconProps) => (

    <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1404_628)">
        <path d="M16 3C16.7652 2.99996 17.5015 3.29233 18.0583 3.81728C18.615 4.34224 18.9501 5.06011 18.995 5.824L19 6V16H19.75C20.397 16 20.93 16.492 20.994 17.122L21 17.25V19C21 19.7652 20.7077 20.5015 20.1827 21.0583C19.6578 21.615 18.9399 21.9501 18.176 21.995L18 22H8C7.23479 22 6.49849 21.7077 5.94174 21.1827C5.38499 20.6578 5.04989 19.9399 5.005 19.176L5 19V9H3.25C2.94054 9.00014 2.64203 8.88549 2.41223 8.67823C2.18244 8.47097 2.03769 8.18583 2.006 7.878L2 7.75V6C1.99996 5.23479 2.29233 4.49849 2.81728 3.94174C3.34224 3.38499 4.06011 3.04989 4.824 3.005L5 3H16ZM19 18H10V19C10 19.35 9.94 19.687 9.83 20H18C18.2652 20 18.5196 19.8946 18.7071 19.7071C18.8946 19.5196 19 19.2652 19 19V18ZM12 12H10C9.74512 12.0003 9.49997 12.0979 9.31463 12.2728C9.1293 12.4478 9.01776 12.687 9.00283 12.9414C8.98789 13.1958 9.07067 13.4464 9.23426 13.6418C9.39785 13.8373 9.6299 13.9629 9.883 13.993L10 14H12C12.2549 13.9997 12.5 13.9021 12.6854 13.7272C12.8707 13.5522 12.9822 13.313 12.9972 13.0586C13.0121 12.8042 12.9293 12.5536 12.7657 12.3582C12.6021 12.1627 12.3701 12.0371 12.117 12.007L12 12ZM14 8H10C9.73478 8 9.48043 8.10536 9.29289 8.29289C9.10536 8.48043 9 8.73478 9 9C9 9.26522 9.10536 9.51957 9.29289 9.70711C9.48043 9.89464 9.73478 10 10 10H14C14.2652 10 14.5196 9.89464 14.7071 9.70711C14.8946 9.51957 15 9.26522 15 9C15 8.73478 14.8946 8.48043 14.7071 8.29289C14.5196 8.10536 14.2652 8 14 8ZM5 5C4.73478 5 4.48043 5.10536 4.29289 5.29289C4.10536 5.48043 4 5.73478 4 6V7H5V5Z"
          fill={isDark ? "#B8BABC" : "#212833"} />
      </g>
      <defs>
        <clipPath id="clip0_1404_628">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  Study: (_props: IconProps) => (
    <svg
      width="30"
      height="30"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M32.5409 16.6396C34.2617 18.3604 35.122 19.2207 35.6695 20.2141C36.3738 21.492 36.7152 22.9382 36.6567 24.3961C36.6113 25.5294 36.2265 26.6837 35.4569 28.9923V28.9923C34.6439 31.4311 34.2374 32.6504 33.3898 33.948C32.4735 35.3507 30.5303 37.14 29.0569 37.9377C27.694 38.6756 26.8258 38.8875 25.0895 39.3112C21.7221 40.133 18.2479 40.6531 14.7835 40.8974C11.4243 41.1342 9.74467 41.2526 8.45471 39.9627C7.16474 38.6728 7.28313 36.9931 7.51989 33.6339C7.76407 30.1695 8.28409 26.6953 9.10572 23.328C9.5294 21.5916 9.74124 20.7234 10.4792 19.3603C11.277 17.8869 13.0663 15.9436 14.4691 15.0273C15.7668 14.1796 16.9862 13.7731 19.4252 12.9602V12.9602C21.7338 12.1907 22.8881 11.8059 24.0214 11.7605C25.4793 11.7021 26.9253 12.0435 28.2031 12.7477C29.1965 13.2952 30.0569 14.1556 31.7776 15.8763L32.5409 16.6396Z"
        fill="#B8BABC"
      />
      <path
        d="M20.8457 24.7436C21.6267 23.9626 22.893 23.9626 23.6741 24.7436C24.4551 25.5247 24.4551 26.791 23.6741 27.572C22.893 28.3531 21.6267 28.3531 20.8457 27.572C20.0646 26.791 20.0646 25.5247 20.8457 24.7436Z"
        fill="#B8BABC"
      />
      <path
        d="M39.2309 17.672C40.012 18.453 41.2783 18.453 42.0593 17.672C42.8404 16.8909 42.8404 15.6246 42.0593 14.8435L39.2309 17.672ZM33.5741 6.35825C32.793 5.5772 31.5267 5.5772 30.7456 6.35825C29.9646 7.1393 29.9646 8.40563 30.7456 9.18667L33.5741 6.35825ZM14.7835 40.8974L14.6428 38.9023L14.7835 40.8974ZM8.45471 39.9627L7.04051 41.3769L8.45471 39.9627ZM14.4691 15.0273L13.3753 13.3529L14.4691 15.0273ZM10.4792 19.3603L8.72045 18.4081L10.4792 19.3603ZM31.7776 15.8763L30.3634 17.2905L31.7776 15.8763ZM28.2031 12.7477L27.2378 14.4993L28.2031 12.7477ZM36.6567 24.3961L38.6551 24.4762L36.6567 24.3961ZM35.6695 20.2141L33.9179 21.1795L35.6695 20.2141ZM33.3898 33.948L31.7154 32.8542L33.3898 33.948ZM29.0569 37.9377L28.1046 36.179L29.0569 37.9377ZM33.9552 15.2254L33.1918 14.4621L30.3634 17.2905L31.1267 18.0538L33.9552 15.2254ZM10.9465 40.2994L22.2599 28.9863L19.4315 26.1578L8.11814 37.471L10.9465 40.2994ZM22.2599 26.1578H22.2599L25.0883 23.3294C23.5262 21.7673 20.9935 21.7673 19.4315 23.3294L22.2599 26.1578ZM22.2599 26.1578V26.1578L25.0883 28.9863C26.6504 27.4242 26.6504 24.8915 25.0883 23.3294L22.2599 26.1578ZM22.2599 26.1578H22.2599L19.4315 28.9863C20.9935 30.5484 23.5262 30.5484 25.0883 28.9863L22.2599 26.1578ZM22.2599 26.1578V26.1578L19.4315 23.3294C17.8694 24.8915 17.8694 27.4242 19.4315 28.9863L22.2599 26.1578ZM42.0593 14.8435L33.5741 6.35825L30.7456 9.18667L39.2309 17.672L42.0593 14.8435ZM7.16272 22.8539C6.31238 26.3389 5.77633 29.9251 5.52484 33.4933L9.51494 33.7745C9.75181 30.4138 10.2558 27.0518 11.0487 23.802L7.16272 22.8539ZM14.9241 42.8924C18.4923 42.6409 22.0786 42.1047 25.5636 41.2542L24.6153 37.3682C21.3656 38.1613 18.0035 38.6654 14.6428 38.9023L14.9241 42.8924ZM5.52484 33.4933C5.41139 35.1029 5.30145 36.5644 5.42509 37.7771C5.56138 39.114 5.99263 40.3291 7.04051 41.3769L9.86891 38.5485C9.62682 38.3064 9.47228 38.0367 9.40446 37.3714C9.32399 36.5821 9.39163 35.5241 9.51494 33.7745L5.52484 33.4933ZM14.6428 38.9023C12.8932 39.0257 11.8353 39.0933 11.0459 39.0129C10.3807 38.9451 10.111 38.7906 9.86891 38.5485L7.04051 41.3769C8.08838 42.4248 9.30343 42.856 10.6403 42.9923C11.8531 43.1159 13.3145 43.0059 14.9241 42.8924L14.6428 38.9023ZM18.7927 11.0628C16.4148 11.8554 14.9305 12.3369 13.3753 13.3529L15.5629 16.7017C16.603 16.0222 17.5577 15.6908 20.0576 14.8576L18.7927 11.0628ZM11.0487 23.802C11.4706 22.0729 11.634 21.4281 12.238 20.3126L8.72045 18.4081C7.84845 20.0187 7.58816 21.1103 7.16272 22.8539L11.0487 23.802ZM13.3753 13.3529C11.6943 14.4509 9.67642 16.6424 8.72045 18.4081L12.238 20.3126C12.8775 19.1313 14.4383 17.4363 15.5629 16.7017L13.3753 13.3529ZM33.1918 14.4621C31.5573 12.8276 30.4695 11.7131 29.1685 10.9961L27.2378 14.4993C27.9235 14.8772 28.5564 15.4835 30.3634 17.2905L33.1918 14.4621ZM20.0576 14.8576C22.4819 14.0495 23.3191 13.7903 24.1015 13.7589L23.9413 9.76212C22.457 9.82159 20.9856 10.3319 18.7927 11.0628L20.0576 14.8576ZM29.1685 10.9961C27.5712 10.1158 25.7636 9.6891 23.9413 9.76212L24.1015 13.7589C25.1949 13.7151 26.2794 13.9711 27.2378 14.4993L29.1685 10.9961ZM37.3542 29.6248C38.0853 27.4319 38.5956 25.9606 38.6551 24.4762L34.6583 24.3159C34.6269 25.0983 34.3677 25.9356 33.5595 28.3598L37.3542 29.6248ZM31.1267 18.0538C32.9337 19.8608 33.54 20.4937 33.9179 21.1795L37.4211 19.2488C36.7041 17.9477 35.5897 16.8599 33.9552 15.2254L31.1267 18.0538ZM38.6551 24.4762C38.7282 22.6538 38.3015 20.8461 37.4211 19.2488L33.9179 21.1795C34.4461 22.1379 34.7022 23.2225 34.6583 24.3159L38.6551 24.4762ZM33.5595 28.3598C32.7262 30.8596 32.3948 31.8142 31.7154 32.8542L35.0642 35.0418C36.08 33.4867 36.5616 32.0026 37.3542 29.6248L33.5595 28.3598ZM25.5636 41.2542C27.3072 40.8287 28.3987 40.5684 30.0091 39.6965L28.1046 36.179C26.9893 36.7829 26.3445 36.9463 24.6153 37.3682L25.5636 41.2542ZM31.7154 32.8542C30.9808 33.9787 29.2859 35.5394 28.1046 36.179L30.0091 39.6965C31.7748 38.7405 33.9661 36.7227 35.0642 35.0418L31.7154 32.8542Z"
        fill="#212833"
      />
    </svg>
  ),

  selectedStudy: ({ isDark }: IconProps) => (
    <svg
      width="30"
      height="30"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M33.1457 7.00947C32.3646 6.22842 31.0983 6.22842 30.3173 7.00947C29.5362 7.79052 29.5362 9.05684 30.3173 9.83789L38.8025 18.3232C39.5836 19.1042 40.8499 19.1042 41.631 18.3232C42.412 17.5421 42.412 16.2758 41.631 15.4947L33.1457 7.00947ZM28.7401 11.6473C27.1428 10.767 25.3353 10.3403 23.513 10.4133C22.0915 10.4703 20.6819 10.9407 18.639 11.6224C18.436 11.6901 18.2325 11.7573 18.0287 11.8245C16.2734 12.4034 14.5038 12.9871 12.947 14.0041C11.266 15.1022 9.24805 17.2936 8.29208 19.0593C7.43088 20.6499 7.16633 21.7344 6.75009 23.4406C5.89316 26.9531 5.35063 30.5385 5.09647 34.1445L5.08588 34.2945C4.97639 35.845 4.87694 37.2534 4.99672 38.4283C5.13301 39.7652 5.56427 40.9803 6.61214 42.0281C7.66001 43.076 8.87506 43.5072 10.212 43.6435C11.4499 43.7697 12.7053 43.6762 13.9465 43.5838C14.1299 43.5702 14.313 43.5565 14.4958 43.5436C18.064 43.2921 21.6502 42.7559 25.1353 41.9054C26.6818 41.528 28.1726 41.1101 29.5808 40.3477C31.3464 39.3917 33.5378 37.3739 34.6358 35.693C35.6526 34.1364 36.2366 32.3661 36.8156 30.611C36.8827 30.4075 36.9498 30.2041 37.0175 30.0014C37.6993 27.9585 38.1697 26.549 38.2267 25.1274C38.2998 23.305 37.8731 21.4973 36.9927 19.9C36.3061 18.654 35.2549 17.6036 33.7316 16.0813L33.7316 16.0813C33.3502 15.7002 32.9505 15.3005 32.5588 14.9085C31.0365 13.3851 29.9861 12.334 28.7401 11.6473ZM24.6601 23.98C23.098 22.4179 20.5654 22.4179 19.0033 23.98C18.1795 24.8038 17.7901 25.8976 17.8352 26.9766C17.0211 27.9502 16.1649 28.9432 15.3173 29.9076L15.2902 29.9384L15.29 29.9386C14.2246 31.1507 13.3942 32.0954 12.8486 32.7529C12.5803 33.0761 12.3347 33.3847 12.1585 33.645C12.0802 33.7608 11.9478 33.9644 11.8472 34.2073C11.8001 34.3208 11.7052 34.5701 11.6821 34.8953C11.6588 35.2233 11.6977 35.9143 12.2749 36.4903C12.8511 37.0654 13.5409 37.1038 13.8694 37.0801C14.1948 37.0566 14.4441 36.9613 14.5577 36.914C14.8007 36.8129 15.0041 36.6801 15.1196 36.6016C15.3796 36.4251 15.6873 36.1791 16.0094 35.9107C16.6648 35.3647 17.6062 34.5339 18.8139 33.468L18.814 33.4679L18.8448 33.4407L21.8275 30.8084C22.8526 30.8095 23.878 30.419 24.6601 29.6369C26.2222 28.0748 26.2222 25.5421 24.6601 23.98ZM12.8513 33.3063C12.8514 33.3061 12.8553 33.3036 12.8626 33.2992C12.8549 33.3043 12.8512 33.3064 12.8513 33.3063ZM15.4578 35.9077C15.4576 35.9078 15.4598 35.9041 15.4649 35.8965C15.4605 35.9037 15.458 35.9075 15.4578 35.9077Z"
        fill={!isDark ? "#212833" : "#B8BABC"}
      />
    </svg>
  ),
  Set: (_props: IconProps) => (
    <svg
      width="30"
      height="30"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.72448 14.2352C8.50642 14.388 7.26203 14.8645 6.74372 15.9773C6.51681 16.4645 6.30992 16.9628 6.12411 17.4714C5.70265 18.6248 6.2459 19.8422 6.99955 20.8119C7.72782 21.7488 8.14504 22.8385 8.14504 24.0012C8.14504 25.164 7.72782 26.2537 6.99955 27.1906C6.2459 28.1602 5.70265 29.3777 6.12411 30.5311C6.30995 31.0397 6.51687 31.5382 6.74382 32.0254C7.26215 33.1382 8.5065 33.6147 9.72453 33.7675C10.9017 33.9152 11.9669 34.3906 12.7889 35.2126C13.6109 36.0346 14.0864 37.0998 14.2341 38.277C14.3869 39.495 14.8634 40.7394 15.9761 41.2577C16.4634 41.4847 16.9618 41.6916 17.4704 41.8774C18.6239 42.2989 19.8413 41.7556 20.8109 41.002C21.7479 40.2737 22.8375 39.8565 24.0003 39.8565C25.163 39.8565 26.2527 40.2737 27.1897 41.002C28.1593 41.7556 29.3767 42.2989 30.5302 41.8774C31.0387 41.6916 31.5372 41.4847 32.0244 41.2577C33.1372 40.7394 33.6137 39.495 33.7665 38.277C33.9142 37.0999 34.3896 36.0346 35.2116 35.2127C36.0336 34.3907 37.0988 33.9152 38.276 33.7675C39.494 33.6147 40.7384 33.1382 41.2567 32.0255C41.4837 31.5382 41.6906 31.0397 41.8764 30.5311C42.2979 29.3777 41.7546 28.1602 41.001 27.1906C40.2727 26.2537 39.8555 25.164 39.8555 24.0012C39.8555 22.8385 40.2727 21.7488 41.001 20.8118C41.7546 19.8422 42.2979 18.6248 41.8764 17.4713C41.6906 16.9628 41.4837 16.4644 41.2568 15.9773C40.7385 14.8645 39.4941 14.388 38.2761 14.2352C37.0989 14.0875 36.0336 13.612 35.2116 12.79C34.3896 11.968 33.9141 10.9027 33.7665 9.72552C33.6137 8.50747 33.1372 7.26309 32.0244 6.74476C31.5372 6.51782 31.0387 6.31091 30.5302 6.12509C29.3767 5.70363 28.1593 6.24688 27.1897 7.00052C26.2527 7.72879 25.163 8.14601 24.0003 8.14601C22.8375 8.14601 21.7478 7.7288 20.8109 7.00052C19.8413 6.24688 18.6239 5.70363 17.4704 6.12509C16.9618 6.31091 16.4634 6.51782 15.9762 6.74475C14.8634 7.26308 14.3869 8.50746 14.2341 9.72552C14.0864 10.9027 13.611 11.968 12.7889 12.79C11.9669 13.612 10.9017 14.0875 9.72448 14.2352Z"
        fill="#B8BABC"
      />
      <path
        d="M30 24C30 27.3137 27.3137 30 24 30C20.6863 30 18 27.3137 18 24C18 20.6863 20.6863 18 24 18C27.3137 18 30 20.6863 30 24Z"
        fill="#B8BABC"
      />
      <path
        d="M9.72448 14.2352C8.50642 14.388 7.26203 14.8645 6.74372 15.9773C6.51681 16.4645 6.30992 16.9628 6.12411 17.4714C5.70265 18.6248 6.2459 19.8422 6.99955 20.8119C7.72782 21.7488 8.14504 22.8385 8.14504 24.0012C8.14504 25.164 7.72782 26.2537 6.99955 27.1906C6.2459 28.1602 5.70265 29.3777 6.12411 30.5311C6.30995 31.0397 6.51687 31.5382 6.74382 32.0254C7.26215 33.1382 8.5065 33.6147 9.72453 33.7675C10.9017 33.9152 11.9669 34.3906 12.7889 35.2126C13.6109 36.0346 14.0864 37.0998 14.2341 38.277C14.3869 39.495 14.8634 40.7394 15.9761 41.2577C16.4634 41.4847 16.9618 41.6916 17.4704 41.8774C18.6239 42.2989 19.8413 41.7556 20.8109 41.002C21.7479 40.2737 22.8375 39.8565 24.0003 39.8565C25.163 39.8565 26.2527 40.2737 27.1897 41.002C28.1593 41.7556 29.3767 42.2989 30.5302 41.8774C31.0387 41.6916 31.5372 41.4847 32.0244 41.2577C33.1372 40.7394 33.6137 39.495 33.7665 38.277C33.9142 37.0999 34.3896 36.0346 35.2116 35.2127C36.0336 34.3907 37.0988 33.9152 38.276 33.7675C39.494 33.6147 40.7384 33.1382 41.2567 32.0255C41.4837 31.5382 41.6906 31.0397 41.8764 30.5311C42.2979 29.3777 41.7546 28.1602 41.001 27.1906C40.2727 26.2537 39.8555 25.164 39.8555 24.0012C39.8555 22.8385 40.2727 21.7488 41.001 20.8118C41.7546 19.8422 42.2979 18.6248 41.8764 17.4713C41.6906 16.9628 41.4837 16.4644 41.2568 15.9773C40.7385 14.8645 39.4941 14.388 38.2761 14.2352C37.0989 14.0875 36.0336 13.612 35.2116 12.79C34.3896 11.968 33.9141 10.9027 33.7665 9.72552C33.6137 8.50747 33.1372 7.26309 32.0244 6.74476C31.5372 6.51782 31.0387 6.31091 30.5302 6.12509C29.3767 5.70363 28.1593 6.24688 27.1897 7.00052C26.2527 7.72879 25.163 8.14601 24.0003 8.14601C22.8375 8.14601 21.7478 7.7288 20.8109 7.00052C19.8413 6.24688 18.6239 5.70363 17.4704 6.12509C16.9618 6.31091 16.4634 6.51782 15.9762 6.74475C14.8634 7.26308 14.3869 8.50746 14.2341 9.72552C14.0864 10.9027 13.611 11.968 12.7889 12.79C11.9669 13.612 10.9017 14.0875 9.72448 14.2352Z"
        stroke="#212833"
        strokeWidth="4"
        strokeLinejoin="round"
      />
      <path
        d="M30 24C30 27.3137 27.3137 30 24 30C20.6863 30 18 27.3137 18 24C18 20.6863 20.6863 18 24 18C27.3137 18 30 20.6863 30 24Z"
        stroke="#212833"
        strokeWidth="4"
        strokeLinejoin="round"
      />
    </svg>
  ),

  selectedSet: ({ isDark }: IconProps) => (
    <svg
      width="30"
      height="30"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.2166 4.24467C29.0536 3.45435 27.0778 4.55257 25.9624 5.41954C25.3311 5.91022 24.6644 6.14412 24.0003 6.14412C23.3363 6.14412 22.6696 5.91022 22.0383 5.41954C20.9229 4.55257 18.9471 3.45435 16.7841 4.24467C16.2216 4.45018 15.6705 4.67898 15.1318 4.92989C13.0459 5.90147 12.4255 8.07321 12.2497 9.4747C12.1502 10.2678 11.8443 10.9045 11.3748 11.3739C10.9053 11.8434 10.2687 12.1494 9.47559 12.2489C8.07411 12.4247 5.90237 13.045 4.93079 15.131C4.67991 15.6696 4.45113 16.2207 4.24565 16.7831C3.45533 18.9461 4.55354 20.9219 5.42052 22.0373C5.9112 22.6686 6.1451 23.3353 6.1451 23.9994C6.1451 24.6634 5.9112 25.3301 5.42052 25.9614C4.55354 27.0768 3.45533 29.0526 4.24565 31.2156C4.45117 31.7781 4.67998 32.3293 4.93091 32.868C5.90249 34.9539 8.07417 35.5742 9.47563 35.75C10.2687 35.8496 10.9053 36.1555 11.3748 36.625C11.8442 37.0944 12.1502 37.731 12.2497 38.5241C12.4255 39.9256 13.0459 42.0972 15.1318 43.0688C15.6705 43.3197 16.2216 43.5485 16.7841 43.754C18.9471 44.5444 20.9229 43.4462 22.0383 42.5792C22.6696 42.0885 23.3363 41.8546 24.0003 41.8546C24.6644 41.8546 25.3311 42.0885 25.9624 42.5792C27.0778 43.4462 29.0536 44.5444 31.2166 43.7541C31.7791 43.5485 32.3302 43.3197 32.8689 43.0688C34.9548 42.0972 35.5752 39.9256 35.751 38.5241C35.8505 37.731 36.1565 37.0944 36.6259 36.625C37.0954 36.1555 37.732 35.8496 38.5251 35.7501C39.9265 35.5742 42.0982 34.9539 43.0698 32.868C43.3207 32.3293 43.5495 31.7781 43.755 31.2156C44.5453 29.0526 43.4471 27.0768 42.5802 25.9614C42.0895 25.3301 41.8556 24.6634 41.8556 23.9994C41.8556 23.3353 42.0895 22.6686 42.5802 22.0373C43.4471 20.9219 44.5453 18.9461 43.755 16.7831C43.5495 16.2207 43.3208 15.6696 43.0699 15.1309C42.0983 13.045 39.9266 12.4247 38.5251 12.2488C37.732 12.1493 37.0954 11.8434 36.6259 11.3739C36.1564 10.9045 35.8505 10.2678 35.751 9.47469C35.5752 8.07321 34.9548 5.90148 32.8689 4.92989C32.3302 4.67898 31.7791 4.45018 31.2166 4.24467ZM18 24C18 20.6863 20.6863 18 24 18C27.3137 18 30 20.6863 30 24C30 27.3137 27.3137 30 24 30C20.6863 30 18 27.3137 18 24ZM24 14C18.4772 14 14 18.4772 14 24C14 29.5228 18.4772 34 24 34C29.5228 34 34 29.5228 34 24C34 18.4772 29.5228 14 24 14Z"
        fill={!isDark ? "#212833" : "#B8BABC"}
      />
    </svg>
  ),
  Content: (_props: IconProps) => (


    <svg width="30" height="30" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="1">
        <path d="M42 24C42 33.9411 33.9411 42 24 42C14.0589 42 6 33.9411 6 24C6 14.0589 14.0589 6 24 6C33.9411 6 42 14.0589 42 24Z" fill="#B8BABC" />
        <path d="M16 27.6667C16 26.7462 16.7462 26 17.6667 26H30.3333C31.2538 26 32 26.7462 32 27.6667V28C32 32.4183 28.4183 36 24 36V36C19.5817 36 16 32.4183 16 28V27.6667Z" fill="#B8BABC" />
      </g>
      <path d="M30 16L28 18L30 20M18 16V20M42 24C42 33.9411 33.9411 42 24 42C14.0589 42 6 33.9411 6 24C6 14.0589 14.0589 6 24 6C33.9411 6 42 14.0589 42 24ZM24 36V36C28.4183 36 32 32.4183 32 28V27.6667C32 26.7462 31.2538 26 30.3333 26H17.6667C16.7462 26 16 26.7462 16 27.6667V28C16 32.4183 19.5817 36 24 36Z"
        stroke="#212833" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
    </svg>


  ),
  selectedContent: ({ isDark }: IconProps) => (
    <svg
      width="30"
      height="30"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4ZM30.8284 16L31.4142 15.4142C32.1953 14.6332 32.1953 13.3668 31.4142 12.5858C30.6332 11.8047 29.3668 11.8047 28.5858 12.5858L26.5858 14.5858C25.8047 15.3668 25.8047 16.6332 26.5858 17.4142L28.5858 19.4142C29.3668 20.1953 30.6332 20.1953 31.4142 19.4142C32.1953 18.6332 32.1953 17.3668 31.4142 16.5858L30.8284 16ZM14 27.6667C14 25.6416 15.6416 24 17.6667 24H30.3333C32.3584 24 34 25.6416 34 27.6667V28C34 33.5228 29.5228 38 24 38C18.4772 38 14 33.5228 14 28V27.6667ZM18 28C18 31.3137 20.6863 34 24 34C27.3137 34 30 31.3137 30 28H18ZM20 14C20 12.8954 19.1046 12 18 12C16.8954 12 16 12.8954 16 14V18C16 19.1046 16.8954 20 18 20C19.1046 20 20 19.1046 20 18V14Z"
        fill={!isDark ? "#212833" : "#B8BABC"}
      />
    </svg>
  ),
}
