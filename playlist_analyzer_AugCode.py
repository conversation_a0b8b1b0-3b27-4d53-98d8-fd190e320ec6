#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网易云歌单分析器
基于网易云音乐API和LLM，分析歌单品味和内心世界
"""

import requests
import json
import time
import re
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlparse, parse_qs

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置类"""
    # 网易云音乐API配置
    netease_api_base: str = "http://localhost:3000"
    
    # LLM API配置（支持OpenAI格式的接口）
    llm_api_base: str = "https://api.openai.com/v1"
    llm_api_key: str = ""
    llm_model: str = "gpt-3.5-turbo"
    
    # 请求配置
    request_delay: float = 0.5  # 请求间隔（秒）
    max_retries: int = 3
    timeout: int = 30


class NeteaseMusicApi:
    """扩展的网易云音乐API客户端"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = config.timeout
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """统一的请求方法"""
        url = f"{self.config.netease_api_base}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if data.get("code") == 200:
                    return data
                else:
                    logger.warning(f"API返回错误: {data.get('message', '未知错误')}")
                    return {"error": data.get('message', '未知错误')}
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    return {"error": f"请求失败: {str(e)}"}
                time.sleep(1)
        
        return {"error": "请求失败"}
    
    def parse_playlist_url(self, url_or_id: str) -> Optional[str]:
        """解析歌单链接或ID"""
        # 如果是纯数字，直接返回
        if url_or_id.isdigit():
            return url_or_id
        
        # 解析URL
        try:
            parsed = urlparse(url_or_id)
            if 'playlist' in parsed.path:
                # 从路径中提取ID
                match = re.search(r'/playlist/(\d+)', parsed.path)
                if match:
                    return match.group(1)
                
                # 从查询参数中提取ID
                query_params = parse_qs(parsed.query)
                if 'id' in query_params:
                    return query_params['id'][0]
        except Exception as e:
            logger.error(f"解析歌单链接失败: {str(e)}")
        
        return None
    
    def get_playlist_detail(self, playlist_id: str) -> Dict:
        """获取歌单详情"""
        return self._make_request(f"/playlist/detail", {"id": playlist_id})
    
    def get_playlist_tracks(self, playlist_id: str) -> Dict:
        """获取歌单所有歌曲"""
        return self._make_request(f"/playlist/track/all", {"id": playlist_id})
    
    def get_song_details(self, song_id: str) -> Dict:
        """获取歌曲详细信息（曲风、标签、语种、BPM）"""
        try:
            data = self._make_request(f"/song/wiki/summary", {"id": song_id})
            
            if "error" in data:
                return data
            
            # 解析基础信息块
            basic_block = next(
                (block for block in data.get("data", {}).get("blocks", [])
                 if block.get("code") == "SONG_PLAY_ABOUT_SONG_BASIC"),
                None
            )
            
            if not basic_block:
                return {"error": "未找到歌曲基础信息"}
            
            result = {
                "song_id": song_id,
                "style": [],  # 曲风
                "tags": [],  # 标签
                "language": "",  # 语种
                "bpm": None  # 节拍数
            }
            
            # 提取各类信息
            for creative in basic_block.get("creatives", []):
                creative_type = creative.get("creativeType")
                resources = creative.get("resources", [])
                text_links = creative.get("uiElement", {}).get("textLinks", [])
                
                if creative_type == "songTag":
                    result["style"] = [res.get("uiElement", {}).get("mainTitle", {}).get("title")
                                       for res in resources if res.get("uiElement")]
                elif creative_type == "songBizTag":
                    result["tags"] = [res.get("uiElement", {}).get("mainTitle", {}).get("title")
                                      for res in resources if res.get("uiElement")]
                elif creative_type == "language" and text_links:
                    result["language"] = text_links[0].get("text", "")
                elif creative_type == "bpm" and text_links:
                    bpm_text = text_links[0].get("text", "")
                    result["bpm"] = int(bpm_text) if bpm_text.isdigit() else None
            
            return result
            
        except Exception as e:
            return {"error": f"解析失败: {str(e)}"}
    
    def get_lyric(self, song_id: str) -> Dict:
        """获取歌曲歌词"""
        data = self._make_request(f"/lyric", {"id": song_id})
        
        if "error" in data:
            return data
        
        return {
            "song_id": song_id,
            "lyric": data.get("lrc", {}).get("lyric", "无歌词信息"),
            "translated_lyric": data.get("tlyric", {}).get("lyric", "")
        }
    
    def get_song_full_info(self, song_id: str, song_name: str = "", artist_name: str = "") -> Dict:
        """获取歌曲完整信息"""
        logger.info(f"获取歌曲信息: {song_name} - {artist_name}")
        
        # 获取基本信息
        song_detail = self._make_request(f"/song/detail", {"ids": song_id})
        if "error" in song_detail:
            return {"error": f"获取歌曲基本信息失败: {song_detail['error']}"}
        
        songs = song_detail.get("songs", [])
        if not songs:
            return {"error": "未找到歌曲信息"}
        
        song_info = songs[0]
        
        # 获取详细信息（曲风、标签等）
        details = self.get_song_details(song_id)
        
        # 获取歌词
        lyric_info = self.get_lyric(song_id)
        
        # 合并信息
        result = {
            "song_id": song_id,
            "name": song_info.get("name", song_name),
            "artists": [ar.get("name") for ar in song_info.get("ar", [])],
            "album": song_info.get("al", {}).get("name", ""),
            "duration": song_info.get("dt", 0) // 1000,  # 转换为秒
            "style": details.get("style", []) if "error" not in details else [],
            "tags": details.get("tags", []) if "error" not in details else [],
            "language": details.get("language", "") if "error" not in details else "",
            "bpm": details.get("bpm") if "error" not in details else None,
            "lyric": lyric_info.get("lyric", "") if "error" not in lyric_info else "",
            # "translated_lyric": lyric_info.get("translated_lyric", "") if "error" not in lyric_info else "",
            "errors": []
        }
        
        # 记录错误信息
        if "error" in details:
            result["errors"].append(f"详细信息获取失败: {details['error']}")
        if "error" in lyric_info:
            result["errors"].append(f"歌词获取失败: {lyric_info['error']}")
        
        time.sleep(self.config.request_delay)  # 控制请求频率
        return result


class LLMAnalyzer:
    """LLM分析器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {config.llm_api_key}",
            "Content-Type": "application/json"
        })
    
    def _call_llm(self, messages: List[Dict], max_tokens: int = 500) -> str:
        """调用LLM API"""
        try:
            payload = {
                "model": self.config.llm_model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": 0.7
            }
            
            response = self.session.post(
                f"{self.config.llm_api_base}/chat/completions",
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            return data["choices"][0]["message"]["content"].strip()
            
        except Exception as e:
            logger.error(f"LLM调用失败: {str(e)}")
            return f"分析失败: {str(e)}"
    
    def analyze_song(self, song_info: Dict) -> str:
        """分析单首歌曲，生成100字总结"""
        # 清理歌词，移除时间戳
        lyric = re.sub(r'\[\d+:\d+\.\d+\]', '', song_info.get("lyric", "")).strip()
        if not lyric or lyric == "无歌词信息":
            lyric = "纯音乐作品"
        
        # 构建分析提示
        song_name = song_info.get("name", "未知歌曲")
        artists = ", ".join(song_info.get("artists", ["未知艺术家"]))
        style = ", ".join(song_info.get("style", ["未知风格"]))
        tags = ", ".join(song_info.get("tags", ["无标签"]))
        language = song_info.get("language", "未知语种")
        bpm = song_info.get("bpm", "未知")
        
        messages = [
            {
                "role": "system",
                "content": "你是一位专业的音乐评论家，擅长分析歌曲的情感内涵和艺术价值。请基于提供的歌曲信息，写一个100字左右的深度总结。"
            },
            {
                "role": "user",
                "content": f"""请分析这首歌曲：

歌曲名：{song_name}
艺术家：{artists}
曲风：{style}
标签：{tags}
语种：{language}
BPM：{bpm}

歌词内容：
{lyric[:1000]}  # 限制歌词长度

请结合曲风、标签、BPM等音乐元素和歌词内容，分析这首歌的情感表达、艺术特色和听感体验，写一个100字左右的专业总结。"""
            }
        ]
        
        return self._call_llm(messages, max_tokens=200)
    
    def analyze_playlist(self, playlist_info: Dict, song_summaries: List[Dict]) -> str:
        """分析整个歌单，生成品味评价"""
        playlist_name = playlist_info.get("name", "未知歌单")
        playlist_desc = playlist_info.get("description", "")
        song_count = len(song_summaries)
        
        # 统计音乐特征
        all_styles = []
        all_tags = []
        all_languages = []
        all_bpms = []
        
        for song in song_summaries:
            all_styles.extend(song.get("style", []))
            all_tags.extend(song.get("tags", []))
            if song.get("language"):
                all_languages.append(song.get("language"))
            if song.get("bpm"):
                all_bpms.append(song.get("bpm"))
        
        # 统计频次
        from collections import Counter
        style_stats = Counter(all_styles).most_common(5)
        tag_stats = Counter(all_tags).most_common(5)
        language_stats = Counter(all_languages).most_common(3)
        
        avg_bpm = sum(all_bpms) / len(all_bpms) if all_bpms else 0
        
        # 收集歌曲总结
        summaries_text = "\n".join([
            f"《{song.get('name', '未知')}》: {song.get('summary', '无总结')}"
            for song in song_summaries[:10]  # 只取前10首作为参考
        ])
        
        messages = [
            {
                "role": "system",
                "content": "你是一位资深的音乐评论家和心理分析师，擅长通过音乐品味分析一个人的内心世界。请用专业、深刻且幽默风趣的语言，对歌单进行锐评。"
            },
            {
                "role": "user",
                "content": f"""请分析这个歌单的品味特征和反映的内心世界：

歌单名称：{playlist_name}
歌单描述：{playlist_desc}
歌曲数量：{song_count}首

音乐特征统计：
主要曲风：{', '.join([f'{style}({count}首)' for style, count in style_stats])}
主要标签：{', '.join([f'{tag}({count}首)' for tag, count in tag_stats])}
主要语种：{', '.join([f'{lang}({count}首)' for lang, count in language_stats])}
平均BPM：{avg_bpm:.1f}

部分歌曲总结：
{summaries_text}

请从以下角度进行分析：
1. 音乐品味特征（曲风偏好、情感倾向等）
2. 内心世界解读（性格特征、情感状态、生活态度等）
3. 专业且幽默的总体评价

要求：语言风趣幽默但不失专业性，深入浅出，字数控制在500字左右。"""
            }
        ]
        
        return self._call_llm(messages, max_tokens=800)


class PlaylistAnalyzer:
    """歌单分析器主类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.netease_api = NeteaseMusicApi(config)
        self.llm_analyzer = LLMAnalyzer(config)
    
    def analyze_playlist(self, playlist_url_or_id: str) -> Dict:
        """分析歌单的完整流程"""
        logger.info("开始分析歌单...")
        
        # 1. 解析歌单ID
        playlist_id = self.netease_api.parse_playlist_url(playlist_url_or_id)
        if not playlist_id:
            return {"error": "无法解析歌单链接或ID"}
        
        logger.info(f"歌单ID: {playlist_id}")
        
        # 2. 获取歌单信息
        playlist_detail = self.netease_api.get_playlist_detail(playlist_id)
        if "error" in playlist_detail:
            return {"error": f"获取歌单信息失败: {playlist_detail['error']}"}
        
        playlist_info = playlist_detail.get("playlist", {})
        playlist_name = playlist_info.get("name", "未知歌单")
        logger.info(f"歌单名称: {playlist_name}")
        
        # 3. 获取歌单歌曲列表
        tracks_data = self.netease_api.get_playlist_tracks(playlist_id)
        if "error" in tracks_data:
            return {"error": f"获取歌曲列表失败: {tracks_data['error']}"}
        
        songs = tracks_data.get("songs", [])
        total_songs = len(songs)
        logger.info(f"歌单共有 {total_songs} 首歌曲")
        
        if total_songs == 0:
            return {"error": "歌单为空"}
        
        # 4. 分析每首歌曲
        analyzed_songs = []
        max_analyze = min(20, total_songs)  # 最多分析20首歌曲
        
        logger.info(f"开始分析前 {max_analyze} 首歌曲...")
        
        for i, song in enumerate(songs[:max_analyze]):
            song_id = str(song.get("id", ""))
            song_name = song.get("name", "")
            artist_name = ", ".join([ar.get("name", "") for ar in song.get("ar", [])])
            
            logger.info(f"正在分析第 {i+1}/{max_analyze} 首: {song_name}")
            
            # 获取歌曲详细信息
            song_info = self.netease_api.get_song_full_info(song_id, song_name, artist_name)
            
            if "error" not in song_info:
                # 使用LLM分析歌曲
                if self.config.llm_api_key:
                    try:
                        summary = self.llm_analyzer.analyze_song(song_info)
                        song_info["summary"] = summary
                    except Exception as e:
                        logger.warning(f"歌曲分析失败: {str(e)}")
                        song_info["summary"] = "分析失败"
                else:
                    song_info["summary"] = "未配置LLM API"
                
                analyzed_songs.append(song_info)
            else:
                logger.warning(f"跳过歌曲 {song_name}: {song_info['error']}")
        
        # 5. 生成歌单整体分析
        playlist_analysis = ""
        if self.config.llm_api_key and analyzed_songs:
            try:
                logger.info("生成歌单整体分析...")
                playlist_analysis = self.llm_analyzer.analyze_playlist(playlist_info, analyzed_songs)
            except Exception as e:
                logger.warning(f"歌单分析失败: {str(e)}")
                playlist_analysis = "歌单分析失败"
        else:
            playlist_analysis = "未配置LLM API或无有效歌曲数据"
        
        # 6. 整理结果
        result = {
            "playlist_info": {
                "id": playlist_id,
                "name": playlist_name,
                "description": playlist_info.get("description", ""),
                "creator": playlist_info.get("creator", {}).get("nickname", ""),
                "total_songs": total_songs,
                "analyzed_songs": len(analyzed_songs)
            },
            "songs": analyzed_songs,
            "playlist_analysis": playlist_analysis,
            "analysis_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        logger.info("歌单分析完成！")
        return result


def main():
    """主程序"""
    # 配置
    config = Config(
        netease_api_base="http://localhost:3000",  # 请修改为你的网易云API地址
        llm_api_base="https://api.openai.com/v1",  # 请修改为你的LLM API地址
        llm_api_key="",  # 请填入你的LLM API密钥
        llm_model="gpt-3.5-turbo"
    )
    
    # 检查配置
    if not config.llm_api_key:
        print("警告: 未配置LLM API密钥，将跳过歌词分析和歌单评价功能")
        print("请在代码中设置 config.llm_api_key")
    
    # 创建分析器
    analyzer = PlaylistAnalyzer(config)
    
    # 获取用户输入
    playlist_input = input("请输入歌单链接或ID: ").strip()
    
    if not playlist_input:
        print("错误: 请输入有效的歌单链接或ID")
        return
    
    # 开始分析
    try:
        result = analyzer.analyze_playlist(playlist_input)
        
        if "error" in result:
            print(f"分析失败: {result['error']}")
            return
        
        # 输出结果
        print("\n" + "="*80)
        print("歌单分析报告")
        print("="*80)
        
        playlist_info = result["playlist_info"]
        print(f"歌单名称: {playlist_info['name']}")
        print(f"创建者: {playlist_info['creator']}")
        print(f"歌曲总数: {playlist_info['total_songs']}")
        print(f"分析歌曲数: {playlist_info['analyzed_songs']}")
        print(f"分析时间: {result['analysis_time']}")
        
        if playlist_info.get('description'):
            print(f"歌单描述: {playlist_info['description']}")
        
        print("\n" + "-"*80)
        print("歌曲分析结果")
        print("-"*80)
        
        for i, song in enumerate(result["songs"][:10], 1):  # 只显示前10首
            print(f"\n{i}. 《{song['name']}》 - {', '.join(song['artists'])}")
            if song.get('style'):
                print(f"   曲风: {', '.join(song['style'])}")
            if song.get('tags'):
                print(f"   标签: {', '.join(song['tags'])}")
            if song.get('language'):
                print(f"   语种: {song['language']}")
            if song.get('bpm'):
                print(f"   BPM: {song['bpm']}")
            if song.get('summary'):
                print(f"   总结: {song['summary']}")
            if song.get('errors'):
                print(f"   错误: {'; '.join(song['errors'])}")
        
        if len(result["songs"]) > 10:
            print(f"\n... 还有 {len(result['songs']) - 10} 首歌曲未显示")
        
        print("\n" + "-"*80)
        print("歌单品味分析")
        print("-"*80)
        print(result["playlist_analysis"])
        
        print("\n" + "="*80)
        print("分析完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断分析")
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        logger.exception("分析过程中出现异常")


if __name__ == "__main__":
    main()
