# 网易云歌单品味分析器

一个基于网易云音乐API和大语言模型的智能歌单分析工具，能够深度分析歌单中每首歌曲的音乐特征，并通过AI生成专业、幽默的品味评价和内心世界解读。

## ✨ 功能特色

- 🎵 **歌单信息获取**: 支持歌单链接或ID输入，自动获取完整歌单信息
- 🎼 **音乐特征分析**: 提取曲风、标签、语种、BPM等详细音乐特征
- 📝 **歌词智能分析**: 使用LLM分析歌词含义和情感表达
- 🤖 **AI歌曲总结**: 为每首歌生成100字专业总结
- 🎭 **品味深度解读**: 基于整个歌单生成幽默风趣的品味分析和内心世界解读
- 🔧 **灵活配置**: 支持多种LLM服务，配置简单

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements_AugCode.txt
```

### 2. 部署网易云音乐API

```bash
# 克隆网易云音乐API项目
git clone https://gitlab.com/Binaryify/neteasecloudmusicapi.git
cd neteasecloudmusicapi

# 安装依赖
npm install

# 启动服务（默认端口3000）
npm start
```

### 3. 配置LLM服务

复制配置文件并填入你的API信息：

```bash
cp config_example_AugCode.py config.py
```

编辑 `config.py` 文件：

```python
@dataclass
class Config:
    # 网易云音乐API地址
    netease_api_base: str = "http://localhost:3000"
    
    # LLM API配置
    llm_api_base: str = "https://api.openai.com/v1"
    llm_api_key: str = "your-api-key-here"  # 填入你的API密钥
    llm_model: str = "gpt-3.5-turbo"
```

### 4. 运行程序

```bash
python playlist_analyzer_AugCode.py
```

## 📖 使用说明

### 支持的输入格式

- **歌单ID**: `123456789`
- **完整链接**: `https://music.163.com/#/playlist?id=123456789`
- **分享链接**: `https://music.163.com/playlist/123456789/`

### 分析流程

1. **歌单解析**: 自动识别并解析歌单链接或ID
2. **信息获取**: 获取歌单基本信息和所有歌曲列表
3. **特征提取**: 为每首歌曲提取音乐特征（曲风、标签、语种、BPM、歌词）
4. **AI分析**: 使用LLM分析歌词含义，生成歌曲总结
5. **品味评价**: 基于所有歌曲信息，生成整体品味分析

### 输出内容

- **歌单基本信息**: 名称、创建者、歌曲数量等
- **歌曲详细分析**: 每首歌的音乐特征和AI总结
- **品味深度解读**: 专业且幽默的整体评价和内心世界分析

## 🔧 配置选项

### 网易云音乐API配置

```python
netease_api_base: str = "http://localhost:3000"  # API服务地址
request_delay: float = 0.5  # 请求间隔，避免限流
max_retries: int = 3  # 最大重试次数
timeout: int = 30  # 请求超时时间
```

### LLM服务配置

支持多种LLM服务：

#### OpenAI
```python
llm_api_base: str = "https://api.openai.com/v1"
llm_api_key: str = "sk-..."
llm_model: str = "gpt-3.5-turbo"
```

#### Azure OpenAI
```python
llm_api_base: str = "https://your-resource.openai.azure.com/openai/deployments/your-deployment"
llm_api_key: str = "your-azure-key"
llm_model: str = "gpt-35-turbo"
```

#### 国内LLM服务（智谱AI示例）
```python
llm_api_base: str = "https://open.bigmodel.cn/api/paas/v4"
llm_api_key: str = "your-api-key"
llm_model: str = "glm-4"
```

## 📊 分析示例

### 歌曲分析示例

```
1. 《夜曲》 - 周杰伦
   曲风: 流行, R&B
   标签: 抒情, 钢琴
   语种: 华语
   BPM: 76
   总结: 这首歌以缓慢的节拍和深情的钢琴伴奏营造出夜晚的静谧氛围，
         周杰伦独特的嗓音诠释着对逝去爱情的眷恋与不舍...
```

### 歌单品味分析示例

```
从这个歌单可以看出，你是一个内心丰富、情感细腻的人。偏爱华语流行和R&B的你，
显然对旋律性和情感表达有着很高的要求。平均76的BPM说明你更喜欢慢节奏的音乐，
这通常反映了一种内省的性格特征...

你的音乐品味透露出一种"文艺青年"的气质，既有对经典的坚持，也有对新声音的
包容。这样的歌单配置，简直就是"我很有故事"的音乐版自我介绍...
```

## ⚠️ 注意事项

1. **API限制**: 请合理控制请求频率，避免触发API限流
2. **歌曲数量**: 程序默认最多分析20首歌曲，可根据需要调整
3. **网络环境**: 确保能正常访问网易云音乐API和LLM服务
4. **API费用**: 使用LLM服务可能产生费用，请注意控制使用量

## 🛠️ 技术架构

- **NeteaseMusicApi**: 网易云音乐API客户端，负责获取歌单和歌曲信息
- **LLMAnalyzer**: LLM分析器，负责歌词分析和总结生成
- **PlaylistAnalyzer**: 主分析器，协调整个分析流程
- **Config**: 配置管理，支持灵活的参数配置

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [NeteaseCloudMusicApi](https://gitlab.com/Binaryify/neteasecloudmusicapi) - 提供网易云音乐API支持
- OpenAI - 提供强大的语言模型服务

---

**享受音乐，理解自己！** 🎵✨
