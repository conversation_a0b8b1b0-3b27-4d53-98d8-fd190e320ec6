#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
用于快速配置和运行歌单分析器
"""

import os
import sys
from playlist_analyzer_AugCode import Config, PlaylistAnalyzer


def setup_config():
    """交互式配置设置"""
    print("🎵 网易云歌单品味分析器 - 快速配置")
    print("=" * 50)
    
    # 网易云API配置
    print("\n1. 网易云音乐API配置")
    netease_api = input("请输入网易云音乐API地址 (默认: http://localhost:3000): ").strip()
    if not netease_api:
        netease_api = "http://localhost:3000"
    
    # LLM API配置
    print("\n2. LLM API配置")
    print("支持的服务:")
    print("  1) OpenAI")
    print("  2) Azure OpenAI")
    print("  3) 智谱AI")
    print("  4) 其他兼容OpenAI格式的服务")
    
    service_choice = input("请选择LLM服务 (1-4): ").strip()
    
    if service_choice == "1":
        # OpenAI
        llm_api_base = "https://api.openai.com/v1"
        llm_model = "gpt-3.5-turbo"
        print("已选择: OpenAI")
    elif service_choice == "2":
        # Azure OpenAI
        resource_name = input("请输入Azure资源名称: ").strip()
        deployment_name = input("请输入部署名称: ").strip()
        llm_api_base = f"https://{resource_name}.openai.azure.com/openai/deployments/{deployment_name}"
        llm_model = "gpt-35-turbo"
        print("已选择: Azure OpenAI")
    elif service_choice == "3":
        # 智谱AI
        llm_api_base = "https://open.bigmodel.cn/api/paas/v4"
        llm_model = "glm-4"
        print("已选择: 智谱AI")
    else:
        # 自定义
        llm_api_base = input("请输入API基础地址: ").strip()
        llm_model = input("请输入模型名称: ").strip()
        print("已选择: 自定义服务")
    
    llm_api_key = input("请输入API密钥: ").strip()
    
    if not llm_api_key:
        print("⚠️  警告: 未输入API密钥，将跳过LLM分析功能")
    
    # 创建配置
    config = Config(
        netease_api_base=netease_api,
        llm_api_base=llm_api_base,
        llm_api_key=llm_api_key,
        llm_model=llm_model
    )
    
    print("\n✅ 配置完成!")
    return config


def test_connection(config):
    """测试连接"""
    print("\n🔍 测试连接...")
    
    # 测试网易云API
    try:
        import requests
        response = requests.get(f"{config.netease_api_base}/", timeout=5)
        print("✅ 网易云音乐API连接正常")
    except Exception as e:
        print(f"❌ 网易云音乐API连接失败: {str(e)}")
        print("请确保NeteaseCloudMusicApi服务正在运行")
        return False
    
    # 测试LLM API
    if config.llm_api_key:
        try:
            import requests
            headers = {
                "Authorization": f"Bearer {config.llm_api_key}",
                "Content-Type": "application/json"
            }
            # 简单的测试请求
            test_payload = {
                "model": config.llm_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 5
            }
            response = requests.post(
                f"{config.llm_api_base}/chat/completions",
                json=test_payload,
                headers=headers,
                timeout=10
            )
            if response.status_code == 200:
                print("✅ LLM API连接正常")
            else:
                print(f"⚠️  LLM API响应异常: {response.status_code}")
        except Exception as e:
            print(f"⚠️  LLM API连接测试失败: {str(e)}")
            print("将跳过LLM分析功能")
    else:
        print("⚠️  未配置LLM API，将跳过相关功能")
    
    return True


def main():
    """主程序"""
    print("🎵 网易云歌单品味分析器")
    print("=" * 50)
    
    # 检查是否需要配置
    need_config = True
    if len(sys.argv) > 1 and sys.argv[1] == "--skip-config":
        need_config = False
        # 使用默认配置
        config = Config()
    else:
        config = setup_config()
    
    # 测试连接
    if not test_connection(config):
        print("\n❌ 连接测试失败，请检查配置")
        return
    
    # 创建分析器
    analyzer = PlaylistAnalyzer(config)
    
    # 获取用户输入
    print("\n" + "=" * 50)
    print("开始分析歌单")
    print("=" * 50)
    
    while True:
        playlist_input = input("\n请输入歌单链接或ID (输入 'quit' 退出): ").strip()
        
        if playlist_input.lower() in ['quit', 'exit', 'q']:
            print("👋 再见!")
            break
        
        if not playlist_input:
            print("❌ 请输入有效的歌单链接或ID")
            continue
        
        # 开始分析
        print(f"\n🚀 开始分析歌单: {playlist_input}")
        print("-" * 50)
        
        try:
            result = analyzer.analyze_playlist(playlist_input)
            
            if "error" in result:
                print(f"❌ 分析失败: {result['error']}")
                continue
            
            # 显示结果
            display_result(result)
            
            # 询问是否保存结果
            save_choice = input("\n💾 是否保存分析结果到文件? (y/n): ").strip().lower()
            if save_choice in ['y', 'yes']:
                save_result(result)
            
        except KeyboardInterrupt:
            print("\n⏹️  用户中断分析")
            continue
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {str(e)}")
            continue


def display_result(result):
    """显示分析结果"""
    print("\n" + "🎵" * 30)
    print("歌单分析报告")
    print("🎵" * 30)
    
    playlist_info = result["playlist_info"]
    print(f"📀 歌单名称: {playlist_info['name']}")
    print(f"👤 创建者: {playlist_info['creator']}")
    print(f"🎼 歌曲总数: {playlist_info['total_songs']}")
    print(f"🔍 分析歌曲数: {playlist_info['analyzed_songs']}")
    print(f"⏰ 分析时间: {result['analysis_time']}")
    
    if playlist_info.get('description'):
        print(f"📝 歌单描述: {playlist_info['description']}")
    
    # 显示歌曲分析（前5首）
    print(f"\n{'🎵' * 20}")
    print("歌曲分析结果 (前5首)")
    print("🎵" * 20)
    
    for i, song in enumerate(result["songs"][:5], 1):
        print(f"\n{i}. 《{song['name']}》 - {', '.join(song['artists'])}")
        
        features = []
        if song.get('style'):
            features.append(f"曲风: {', '.join(song['style'])}")
        if song.get('tags'):
            features.append(f"标签: {', '.join(song['tags'])}")
        if song.get('language'):
            features.append(f"语种: {song['language']}")
        if song.get('bpm'):
            features.append(f"BPM: {song['bpm']}")
        
        if features:
            print(f"   🏷️  {' | '.join(features)}")
        
        if song.get('summary'):
            print(f"   💭 {song['summary']}")
    
    if len(result["songs"]) > 5:
        print(f"\n... 还有 {len(result['songs']) - 5} 首歌曲")
    
    # 显示品味分析
    print(f"\n{'🎭' * 20}")
    print("品味分析")
    print("🎭" * 20)
    print(result["playlist_analysis"])


def save_result(result):
    """保存分析结果"""
    import json
    from datetime import datetime
    
    # 生成文件名
    playlist_name = result["playlist_info"]["name"]
    safe_name = "".join(c for c in playlist_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"歌单分析_{safe_name}_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"✅ 分析结果已保存到: {filename}")
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")


if __name__ == "__main__":
    main()
