#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件示例
请复制此文件为 config.py 并填入你的配置信息
"""

from dataclasses import dataclass


@dataclass
class Config:
    """配置类"""
    
    # ==================== 网易云音乐API配置 ====================
    # 网易云音乐API服务地址
    # 需要部署 NeteaseCloudMusicApi 项目
    # 项目地址: https://gitlab.com/Binaryify/neteasecloudmusicapi
    netease_api_base: str = "http://localhost:3000"
    
    # ==================== LLM API配置 ====================
    # 支持OpenAI格式的LLM API
    # 可以使用 OpenAI、Azure OpenAI、或其他兼容的API服务
    
    # API基础地址
    llm_api_base: str = "https://api.openai.com/v1"
    
    # API密钥
    llm_api_key: str = "your-api-key-here"
    
    # 模型名称
    llm_model: str = "gpt-3.5-turbo"
    
    # ==================== 请求配置 ====================
    # 请求间隔（秒），避免API限流
    request_delay: float = 0.5
    
    # 最大重试次数
    max_retries: int = 3
    
    # 请求超时时间（秒）
    timeout: int = 30


# ==================== 其他LLM服务配置示例 ====================

# Azure OpenAI 配置示例
class AzureConfig(Config):
    llm_api_base: str = "https://your-resource.openai.azure.com/openai/deployments/your-deployment"
    llm_api_key: str = "your-azure-api-key"
    llm_model: str = "gpt-35-turbo"  # Azure中的模型名称


# 国内LLM服务配置示例（如智谱AI、百度文心等）
class DomesticLLMConfig(Config):
    llm_api_base: str = "https://open.bigmodel.cn/api/paas/v4"  # 智谱AI示例
    llm_api_key: str = "your-domestic-api-key"
    llm_model: str = "glm-4"


# ==================== 使用说明 ====================
"""
1. 部署网易云音乐API:
   - 克隆项目: git clone https://gitlab.com/Binaryify/neteasecloudmusicapi.git
   - 安装依赖: npm install
   - 启动服务: npm start
   - 默认端口: 3000

2. 配置LLM API:
   - 获取API密钥
   - 修改 llm_api_base 和 llm_api_key
   - 根据服务商调整 llm_model

3. 运行程序:
   python playlist_analyzer_AugCode.py

4. 支持的歌单输入格式:
   - 歌单ID: 123456789
   - 歌单链接: https://music.163.com/#/playlist?id=123456789
   - 分享链接: https://music.163.com/playlist/123456789/
"""
