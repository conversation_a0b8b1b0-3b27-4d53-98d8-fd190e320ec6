import requests
import json


class NeteaseMusicApi:
    def __init__(self, api_base_url="https://wyy.jrenc.com"):
        """
        初始化网易云音乐API调用器
        :param api_base_url: API服务基础地址（需自行部署NeteaseCloudMusicApi）
        """
        self.api_base = api_base_url

    def get_song_details(self, song_id):
        """
        获取歌曲详细信息（曲风、标签、语种、BPM）
        :param song_id: 歌曲ID
        :return: 包含详细信息的字典
        """
        try:
            url = f"{self.api_base}/song/wiki/summary?id={song_id}"
            response = requests.get(url)
            response.raise_for_status()  # 检查请求是否成功
            data = response.json()

            if data.get("code") != 200:
                return {"error": f"API返回错误: {data.get('message', '未知错误')}"}

            # 解析基础信息块
            basic_block = next(
                (block for block in data.get("data", {}).get("blocks", [])
                 if block.get("code") == "SONG_PLAY_ABOUT_SONG_BASIC"),
                None
            )

            if not basic_block:
                return {"error": "未找到歌曲基础信息"}

            result = {
                "song_id": song_id,
                "style": [],  # 曲风
                "tags": [],  # 标签
                "language": "",  # 语种
                "bpm": None  # 节拍数
            }

            # 提取各类信息
            for creative in basic_block.get("creatives", []):
                creative_type = creative.get("creativeType")
                resources = creative.get("resources", [])
                text_links = creative.get("uiElement", {}).get("textLinks", [])

                if creative_type == "songTag":
                    result["style"] = [res.get("uiElement", {}).get("mainTitle", {}).get("title")
                                       for res in resources if res.get("uiElement")]
                elif creative_type == "songBizTag":
                    result["tags"] = [res.get("uiElement", {}).get("mainTitle", {}).get("title")
                                      for res in resources if res.get("uiElement")]
                elif creative_type == "language" and text_links:
                    result["language"] = text_links[0].get("text", "")
                elif creative_type == "bpm" and text_links:
                    bpm_text = text_links[0].get("text", "")
                    result["bpm"] = int(bpm_text) if bpm_text.isdigit() else None

            return result

        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
        except Exception as e:
            return {"error": f"解析失败: {str(e)}"}

    def get_lyric(self, song_id):
        """
        获取歌曲歌词
        :param song_id: 歌曲ID
        :return: 包含歌词的字典
        """
        try:
            url = f"{self.api_base}/lyric?id={song_id}"
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()

            if data.get("code") != 200:
                return {"error": f"获取歌词失败: {data.get('message', '未知错误')}"}

            return {
                "song_id": song_id,
                "lyric": data.get("lrc", {}).get("lyric", "无歌词信息"),
                "translated_lyric": data.get("tlyric", {}).get("lyric", "无翻译歌词")
            }

        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
        except Exception as e:
            return {"error": f"解析失败: {str(e)}"}

    def get_song_full_info(self, song_id):
        """
        获取歌曲完整信息（合并详细信息和歌词）
        :param song_id: 歌曲ID
        :return: 合并后的信息字典
        """
        details = self.get_song_details(song_id)
        lyric = self.get_lyric(song_id)

        # 合并结果（如果有错误则保留错误信息）
        if "error" in details:
            return details
        if "error" in lyric:
            details["lyric_error"] = lyric["error"]
        else:
            details.update({
                "lyric": lyric["lyric"],
                "translated_lyric": lyric["translated_lyric"]
            })
        return details


if __name__ == "__main__":
    # 初始化API客户端（需替换为自己部署的API地址）
    api = NeteaseMusicApi(api_base_url="http://localhost:3322/")

    # 示例：查询歌曲ID为1407171464的信息（可替换为其他歌曲ID）
    song_id = "1407171464"

    # 获取完整信息
    full_info = api.get_song_full_info(song_id)

    # 打印结果
    print(json.dumps(full_info, ensure_ascii=False, indent=2))